import pandas as pd
import re
from collections import defaultdict

# Read the CSV file
df = pd.read_csv('survey.csv')

# Define categories and their keywords/patterns
categories = {
    'Phone Issues/Replacement': [
        'spoil', 'spoilt', 'broken', 'damage', 'damaged', 'faulty', 'problem', 'issue', 
        'lag', 'slow', 'crash', 'malfunction', 'not working', 'battery', 'dying', 
        'deteriorat', 'drain', 'weak', 'old phone', 'outdated', 'aging', 'ancient',
        'green line', 'crack', 'screen', 'dead pixel', 'water damage', 'motherboard'
    ],
    'Contract/Plan Related': [
        'contract', 'recontract', 'renewal', 'renew', 'expired', 'expir', 'due', 
        'end', 'plan', 'eligible', 'eligibility', '24 month', '2 year', 'corporate',
        'CIS', 'bespoke'
    ],
    'Promotions/Deals': [
        'promotion', 'promo', 'deal', 'offer', 'discount', 'anniversary', 'birthday',
        'trade in', 'trade-in', 'voucher', 'rebate', 'free', '$0', 'zero dollar',
        'special', 'attractive', 'good price', 'cheap', 'reasonable', 'affordable',
        'IT show', 'sale'
    ],
    'Technology Upgrade': [
        'upgrade', 'latest', 'new model', 'newer', 'better', 'advanced', 'technology',
        'tech', '5G', '4G', 'AI', 'feature', 'function', 'camera', 'storage', 
        'memory', 'bigger screen', 'performance', 'faster', 'iOS', 'android',
        'iPhone 16', 'iPhone 15', 'Samsung', 'fold', 'foldable'
    ],
    'Personal/Gift': [
        'gift', 'present', 'birthday', 'wife', 'husband', 'daughter', 'son', 
        'child', 'kid', 'parent', 'family', 'mother', 'father', 'spouse',
        'girlfriend', 'boyfriend', 'dream', 'show off', 'personal'
    ],
    'Service/Network': [
        'network', 'coverage', 'roaming', 'data', 'GB', 'service', 'M1', 
        'loyal', 'customer', 'staff', 'friendly', 'helpful', 'recommend'
    ],
    'Work/Business': [
        'work', 'company', 'corporate', 'business', 'office', 'professional',
        'requirement', 'policy', 'additional phone', 'spare phone', 'second phone'
    ],
    'Convenience/Timing': [
        'convenient', 'timing', 'right time', 'good time', 'happened to', 
        'pass by', 'coinciden', 'just nice', 'about time', 'feel like',
        'why not', 'easy', 'smooth', 'hassle free'
    ]
}

def categorize_response(response):
    """Categorize a response based on keywords"""
    if pd.isna(response) or response.strip().lower() in ['na', 'nil', 'no', 'none', '']:
        return 'No Response/Unclear'
    
    response_lower = str(response).lower()
    matched_categories = []
    
    for category, keywords in categories.items():
        for keyword in keywords:
            if keyword in response_lower:
                matched_categories.append(category)
                break
    
    # If multiple categories match, prioritize based on importance
    if len(matched_categories) > 1:
        priority_order = [
            'Phone Issues/Replacement',
            'Promotions/Deals', 
            'Contract/Plan Related',
            'Technology Upgrade',
            'Personal/Gift',
            'Work/Business',
            'Service/Network',
            'Convenience/Timing'
        ]
        for category in priority_order:
            if category in matched_categories:
                return category
    elif len(matched_categories) == 1:
        return matched_categories[0]
    else:
        # Manual categorization for unclear responses
        if any(word in response_lower for word in ['change', 'new', 'replace']):
            return 'Technology Upgrade'
        elif any(word in response_lower for word in ['need', 'want', 'like']):
            return 'Personal/Gift'
        else:
            return 'No Response/Unclear'

# Apply categorization
df['Category'] = df['Motivation to Purchase'].apply(categorize_response)

# Generate report
category_counts = df['Category'].value_counts()
total_responses = len(df)

print("="*60)
print("SURVEY ANALYSIS REPORT")
print("="*60)
print(f"Total Responses: {total_responses}")
print()

print("CATEGORY BREAKDOWN:")
print("-" * 40)
for category, count in category_counts.items():
    percentage = (count / total_responses) * 100
    print(f"{category:<25}: {count:>4} ({percentage:>5.1f}%)")

print()
print("DETAILED CATEGORY ANALYSIS:")
print("=" * 60)

for category in category_counts.index:
    print(f"\n{category.upper()}")
    print("-" * len(category))
    category_responses = df[df['Category'] == category]['Motivation to Purchase']
    print(f"Count: {len(category_responses)} responses")
    
    # Show sample responses (first 3)
    print("Sample responses:")
    for i, response in enumerate(category_responses.head(3)):
        print(f"  {i+1}. {response}")
    
    if len(category_responses) > 3:
        print(f"  ... and {len(category_responses) - 3} more")

# Save detailed results to CSV
df.to_csv('survey_categorized.csv', index=False)
print(f"\nDetailed results saved to 'survey_categorized.csv'")
