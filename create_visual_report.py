import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.patches import Wedge
import numpy as np

# Set style
plt.style.use('default')
sns.set_palette("husl")

# Read the categorized data
df = pd.read_csv('survey_categorized.csv')

# Get category counts
category_counts = df['Category'].value_counts()
total_responses = len(df)

# Create figure with subplots
fig = plt.figure(figsize=(16, 12))

# 1. Pie Chart
ax1 = plt.subplot(2, 2, 1)
colors = plt.cm.Set3(np.linspace(0, 1, len(category_counts)))
wedges, texts, autotexts = ax1.pie(category_counts.values, 
                                   labels=category_counts.index, 
                                   autopct='%1.1f%%',
                                   startangle=90,
                                   colors=colors)

# Improve text readability
for autotext in autotexts:
    autotext.set_color('black')
    autotext.set_fontweight('bold')
    autotext.set_fontsize(8)

for text in texts:
    text.set_fontsize(8)

ax1.set_title('Distribution of Purchase Motivations\n(Pie Chart)', fontsize=14, fontweight='bold', pad=20)

# 2. Horizontal Bar Chart
ax2 = plt.subplot(2, 2, 2)
y_pos = np.arange(len(category_counts))
bars = ax2.barh(y_pos, category_counts.values, color=colors)

# Add value labels on bars
for i, (bar, count) in enumerate(zip(bars, category_counts.values)):
    ax2.text(bar.get_width() + 5, bar.get_y() + bar.get_height()/2, 
             f'{count} ({count/total_responses*100:.1f}%)', 
             ha='left', va='center', fontweight='bold', fontsize=9)

ax2.set_yticks(y_pos)
ax2.set_yticklabels(category_counts.index, fontsize=10)
ax2.set_xlabel('Number of Responses', fontsize=12, fontweight='bold')
ax2.set_title('Purchase Motivations by Category\n(Bar Chart)', fontsize=14, fontweight='bold', pad=20)
ax2.grid(axis='x', alpha=0.3)

# 3. Top Categories Focus
ax3 = plt.subplot(2, 2, 3)
top_5 = category_counts.head(5)
bars3 = ax3.bar(range(len(top_5)), top_5.values, color=colors[:5])

# Add value labels on bars
for i, (bar, count) in enumerate(zip(bars3, top_5.values)):
    ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 5, 
             f'{count}\n({count/total_responses*100:.1f}%)', 
             ha='center', va='bottom', fontweight='bold', fontsize=10)

ax3.set_xticks(range(len(top_5)))
ax3.set_xticklabels([label.replace('/', '/\n') for label in top_5.index], 
                    rotation=45, ha='right', fontsize=9)
ax3.set_ylabel('Number of Responses', fontsize=12, fontweight='bold')
ax3.set_title('Top 5 Purchase Motivations', fontsize=14, fontweight='bold', pad=20)
ax3.grid(axis='y', alpha=0.3)

# 4. Summary Statistics Table
ax4 = plt.subplot(2, 2, 4)
ax4.axis('off')

# Create summary table data
table_data = []
for category, count in category_counts.items():
    percentage = count / total_responses * 100
    table_data.append([category, count, f"{percentage:.1f}%"])

# Add total row
table_data.append(['TOTAL', total_responses, '100.0%'])

# Create table
table = ax4.table(cellText=table_data,
                  colLabels=['Category', 'Count', 'Percentage'],
                  cellLoc='left',
                  loc='center',
                  colWidths=[0.5, 0.2, 0.2])

table.auto_set_font_size(False)
table.set_fontsize(9)
table.scale(1, 2)

# Style the table
for i in range(len(table_data) + 1):  # +1 for header
    for j in range(3):
        cell = table[(i, j)]
        if i == 0:  # Header
            cell.set_facecolor('#4CAF50')
            cell.set_text_props(weight='bold', color='white')
        elif i == len(table_data):  # Total row
            cell.set_facecolor('#E0E0E0')
            cell.set_text_props(weight='bold')
        else:
            cell.set_facecolor('#F5F5F5' if i % 2 == 0 else 'white')

ax4.set_title('Summary Statistics', fontsize=14, fontweight='bold', pad=20)

plt.tight_layout()
plt.savefig('survey_analysis_report.png', dpi=300, bbox_inches='tight')
plt.show()

# Create a detailed insights report
print("\n" + "="*80)
print("DETAILED INSIGHTS REPORT")
print("="*80)

print(f"\nSURVEY OVERVIEW:")
print(f"• Total responses analyzed: {total_responses:,}")
print(f"• Categories identified: {len(category_counts)}")
print(f"• Most common motivation: {category_counts.index[0]} ({category_counts.iloc[0]} responses)")

print(f"\nKEY FINDINGS:")
print(f"1. PROMOTIONS/DEALS dominates with {category_counts['Promotions/Deals']} responses (28.4%)")
print(f"   - Customers are highly price-sensitive and promotion-driven")
print(f"   - Trade-in deals and anniversary offers are major motivators")

print(f"\n2. PHONE ISSUES/REPLACEMENT is second with {category_counts['Phone Issues/Replacement']} responses (19.5%)")
print(f"   - Many customers upgrade due to technical problems")
print(f"   - Battery issues, slow performance, and hardware failures drive purchases")

print(f"\n3. TECHNOLOGY UPGRADE accounts for {category_counts['Technology Upgrade']} responses (17.4%)")
print(f"   - Desire for latest features, better cameras, and newer models")
print(f"   - Technology advancement is a significant motivator")

print(f"\n4. CONTRACT/PLAN RELATED represents {category_counts['Contract/Plan Related']} responses (13.2%)")
print(f"   - Contract renewals and plan changes drive upgrades")
print(f"   - Corporate plans and eligibility timing influence decisions")

print(f"\nRECOMMendations:")
print("• Focus marketing on promotional offers and trade-in programs")
print("• Highlight reliability and performance improvements in messaging")
print("• Time campaigns around contract renewal periods")
print("• Emphasize latest technology features in product positioning")

print(f"\nNote: {category_counts['No Response/Unclear']} responses (15.7%) were unclear or non-specific")
