import pandas as pd
import os
from openai import AzureOpenAI
from dotenv import load_dotenv
import json
import time
from collections import defaultdict
import re

# Load environment variables
load_dotenv()

# Initialize Azure OpenAI client
client = AzureOpenAI(
    azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
    api_key=os.getenv("AZURE_OPENAI_API_KEY"),
    api_version=os.getenv("AZURE_OPENAI_API_VERSION")
)

def categorize_with_ai(responses_batch):
    """Use Azure OpenAI to categorize a batch of responses"""
    
    prompt = f"""
You are an expert market researcher analyzing customer motivations for phone purchases. 
Categorize each response into ONE of these categories (no "Other" category allowed):

1. **Promotions/Deals** - Discounts, trade-ins, special offers, anniversary sales, vouchers
2. **Phone Issues/Problems** - Battery problems, slow performance, hardware failures, old phone issues
3. **Technology Upgrade** - Latest features, better camera, 5G, storage, new models
4. **Contract/Plan Related** - Contract renewal, plan changes, corporate plans, eligibility
5. **Personal/Family** - Gifts, family needs, personal preferences, lifestyle
6. **Service/Network** - Network quality, customer service, carrier loyalty
7. **Work/Business** - Business needs, work requirements, additional phones
8. **Convenience/Timing** - Right timing, convenience, spontaneous decisions

For unclear/empty responses, choose the most likely category based on context clues.

Responses to categorize:
{chr(10).join([f"{i+1}. {resp}" for i, resp in enumerate(responses_batch)])}

Return ONLY a JSON array with the category names in order, like:
["Promotions/Deals", "Phone Issues/Problems", "Technology Upgrade", ...]
"""

    try:
        response = client.chat.completions.create(
            model=os.getenv("AZURE_OPENAI_MODEL"),
            messages=[
                {"role": "system", "content": "You are a precise market research analyst. Return only the requested JSON format."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.1,
            max_tokens=500
        )
        
        result = response.choices[0].message.content.strip()
        # Extract JSON from response
        json_match = re.search(r'\[.*\]', result, re.DOTALL)
        if json_match:
            categories = json.loads(json_match.group())
            return categories
        else:
            print(f"Failed to parse JSON from: {result}")
            return ["Technology Upgrade"] * len(responses_batch)  # fallback
            
    except Exception as e:
        print(f"API Error: {e}")
        return ["Technology Upgrade"] * len(responses_batch)  # fallback

def main():
    print("Loading survey data...")
    df = pd.read_csv('survey.csv')
    
    # Clean the data
    responses = df['Motivation to Purchase'].fillna('').astype(str).tolist()
    
    print(f"Processing {len(responses)} responses with Azure OpenAI...")
    
    # Process in batches to avoid token limits
    batch_size = 10
    all_categories = []
    
    for i in range(0, len(responses), batch_size):
        batch = responses[i:i+batch_size]
        print(f"Processing batch {i//batch_size + 1}/{(len(responses)-1)//batch_size + 1}...")
        
        categories = categorize_with_ai(batch)
        all_categories.extend(categories)
        
        # Rate limiting
        time.sleep(1)
    
    # Add categories to dataframe
    df['AI_Category'] = all_categories
    
    # Generate analysis
    category_counts = df['AI_Category'].value_counts()
    total_responses = len(df)
    
    print("\n" + "="*80)
    print("AZURE OPENAI ENHANCED SURVEY ANALYSIS")
    print("="*80)
    
    print(f"\nSURVEY OVERVIEW:")
    print(f"• Total responses: {total_responses:,}")
    print(f"• AI-powered categorization using Azure OpenAI GPT-4")
    print(f"• Categories identified: {len(category_counts)}")
    
    print(f"\nCATEGORY BREAKDOWN:")
    print("-" * 60)
    for i, (category, count) in enumerate(category_counts.items(), 1):
        percentage = (count / total_responses) * 100
        bar = "█" * int(percentage / 2)  # Visual bar
        print(f"{i:2}. {category:<22}: {count:>4} ({percentage:>5.1f}%) {bar}")
    
    # Detailed analysis for top categories
    print(f"\nDETAILED CATEGORY INSIGHTS:")
    print("=" * 80)
    
    for category in category_counts.head(5).index:
        count = category_counts[category]
        percentage = (count / total_responses) * 100
        
        print(f"\n🎯 {category.upper()}")
        print("-" * (len(category) + 4))
        print(f"Count: {count} responses ({percentage:.1f}%)")
        
        # Get sample responses
        samples = df[df['AI_Category'] == category]['Motivation to Purchase'].head(5).tolist()
        print("Sample responses:")
        for i, sample in enumerate(samples, 1):
            # Truncate long responses
            display_sample = sample if len(str(sample)) <= 100 else str(sample)[:97] + "..."
            print(f"  {i}. \"{display_sample}\"")
    
    # Business insights
    print(f"\n📊 BUSINESS INSIGHTS:")
    print("=" * 80)
    
    # Calculate key metrics
    price_sensitive = category_counts.get('Promotions/Deals', 0)
    problem_driven = category_counts.get('Phone Issues/Problems', 0)
    tech_driven = category_counts.get('Technology Upgrade', 0)
    contract_driven = category_counts.get('Contract/Plan Related', 0)
    
    print(f"\n💰 PRICE SENSITIVITY: {price_sensitive/total_responses*100:.1f}%")
    print(f"   • Customers highly responsive to promotions and deals")
    print(f"   • Trade-in programs and discounts are key motivators")
    
    print(f"\n🔧 PROBLEM-DRIVEN PURCHASES: {problem_driven/total_responses*100:.1f}%")
    print(f"   • Significant portion upgrading due to device issues")
    print(f"   • Focus on reliability messaging for retention")
    
    print(f"\n📱 TECHNOLOGY ENTHUSIASM: {tech_driven/total_responses*100:.1f}%")
    print(f"   • Strong appetite for latest features and models")
    print(f"   • Camera and performance improvements are key selling points")
    
    print(f"\n📋 CONTRACT INFLUENCE: {contract_driven/total_responses*100:.1f}%")
    print(f"   • Contract timing creates natural upgrade opportunities")
    print(f"   • Corporate plans drive bulk business")
    
    # Strategic recommendations
    print(f"\n🎯 STRATEGIC RECOMMENDATIONS:")
    print("=" * 80)
    
    print(f"\n1. MARKETING FOCUS:")
    print(f"   • Emphasize promotional offers in primary campaigns")
    print(f"   • Highlight trade-in value and cost savings")
    print(f"   • Time major promotions with contract renewal cycles")
    
    print(f"\n2. PRODUCT MESSAGING:")
    print(f"   • Lead with reliability and performance improvements")
    print(f"   • Showcase camera and technology advancements")
    print(f"   • Emphasize battery life and durability")
    
    print(f"\n3. CUSTOMER SEGMENTATION:")
    top_3_categories = category_counts.head(3)
    for category, count in top_3_categories.items():
        pct = count/total_responses*100
        print(f"   • {category} ({pct:.1f}%): Tailor messaging accordingly")
    
    # Save results
    df.to_csv('survey_ai_categorized.csv', index=False)
    print(f"\n✅ Enhanced analysis complete!")
    print(f"📁 Detailed results saved to 'survey_ai_categorized.csv'")
    
    return df, category_counts

if __name__ == "__main__":
    df, category_counts = main()
